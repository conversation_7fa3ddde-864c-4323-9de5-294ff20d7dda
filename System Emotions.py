import cv2
import mindspore as ms
from mindspore import Tensor
from mindspore.train.serialization import load_checkpoint, load_param_into_net
from PIL import Image, ImageTk
import numpy as np
from mindspore.common.initializer import Normal
import mindspore.nn as nn
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import datetime
import os
import json
import csv
import random
import hashlib
import mysql.connector
from mysql.connector import Error
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.pyplot as plt
from pathlib import Path
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 或 ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# 确保中文显示正常
plt.rcParams["font.family"] = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC"]

# 表情类别
categories = ['生气', '开心', '悲伤']
# 扩展后的情绪类别
extended_categories = ['生气', '开心', '悲伤']

# 情绪颜色映射
emotion_colors = {
    '生气': '#FF5252',  # 红色
    '开心': '#4CAF50',  # 绿色
    '悲伤': '#2196F3',  # 蓝色
}

# 情绪图标映射（使用文本符号代替emoji）
emotion_icons = {
    '生气': '😠',
    '开心': '😄',
    '悲伤': '😢'
}

# 人脸检测器
try:
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
except:
    # 如果无法加载OpenCV自带的分类器，尝试使用本地路径
    face_cascade = cv2.CascadeClassifier('haarcascade_frontalface_default.xml')

# 定义模型
class CNN(nn.Cell):
    def __init__(self):
        super(CNN, self).__init__()
        self.conv1 = nn.Conv2d(3, 32, 3, pad_mode='same', weight_init=Normal(0.02))
        self.conv2 = nn.Conv2d(32, 64, 3, pad_mode='same', weight_init=Normal(0.02))
        self.conv3 = nn.Conv2d(64, 128, 3, pad_mode='same', weight_init=Normal(0.02))
        self.fc1 = nn.Dense(128 * 6 * 6, 512)
        self.fc2 = nn.Dense(512, len(categories))
        self.relu = nn.ReLU()
        self.max_pool2d = nn.MaxPool2d(kernel_size=2, stride=2)
        self.flatten = nn.Flatten()

    def construct(self, x):
        x = self.conv1(x)
        x = self.relu(x)
        x = self.max_pool2d(x)
        x = self.conv2(x)
        x = self.relu(x)
        x = self.max_pool2d(x)
        x = self.conv3(x)
        x = self.relu(x)
        x = self.max_pool2d(x)
        x = self.flatten(x)
        x = self.fc1(x)
        x = self.relu(x)
        x = self.fc2(x)
        return x

# 数据库连接类
class DatabaseConnection:
    def __init__(self):
        self.config = {
            'host': 'localhost',
            'user': 'root',
            'password': '123456',
            'database': 'emotion_recognition',
            'port': 3306,
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
    
    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = mysql.connector.connect(**self.config)
            if self.connection.is_connected():
                print("已成功连接到MySQL数据库")
                return True
        except Error as e:
            print(f"数据库连接错误: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            print("已断开数据库连接")
    
    def execute_query(self, query, params=None):
        """执行SQL查询"""
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            return cursor
        except Error as e:
            print(f"执行查询错误: {e}")
            return None
    
    def create_tables(self):
        """创建必要的数据库表"""
        try:
            cursor = self.execute_query("""
                CREATE TABLE IF NOT EXISTS users (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    username VARCHAR(500) UNIQUE NOT NULL,
                    password_hash VARCHAR(1024) NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            cursor = self.execute_query("""
                CREATE TABLE IF NOT EXISTS emotion_history (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    emotion TEXT NOT NULL,
                    anger REAL NOT NULL,
                    happy REAL NOT NULL,
                    sad REAL NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            """)
            
            cursor = self.execute_query("""
                CREATE TABLE IF NOT EXISTS screenshots (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    file_path TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    emotion TEXT NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            """)
            
            cursor = self.execute_query("""
                CREATE TABLE IF NOT EXISTS user_settings (
                    user_id INT PRIMARY KEY,
                    alert_enabled TINYINT(1) NOT NULL DEFAULT 1,
                    anger_threshold FLOAT NOT NULL DEFAULT 0.85,
                    sad_threshold FLOAT NOT NULL DEFAULT 0.80,
                    camera_index INT NOT NULL DEFAULT 0,
                    auto_save_screenshots TINYINT(1) NOT NULL DEFAULT 0,
                    screenshot_interval INT NOT NULL DEFAULT 60,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            """)
            
            print("数据库表创建完成")
        except Error as e:
            print(f"创建表错误: {e}")

# 用户管理类
class UserManager:
    def __init__(self, db):
        self.db = db
        self.current_user = None
        self.current_user_id = None
    
    def hash_password(self, password):
        """对密码进行哈希处理"""
        return hashlib.sha256(password.encode('utf-8')).hexdigest()
    
    def register_user(self, username, password):
        """注册新用户"""
        if not username or not password:
            return False, "用户名和密码不能为空"
        
        password_hash = self.hash_password(password)
        
        try:
            # 检查用户名是否已存在
            cursor = self.db.execute_query(
                "SELECT id FROM users WHERE username = %s", (username,)
            )
            if cursor and cursor.fetchone():
                return False, "用户名已存在"
            
            # 创建新用户
            cursor = self.db.execute_query(
                "INSERT INTO users (username, password_hash) VALUES (%s, %s)",
                (username, password_hash)
            )
            
            # 获取新用户ID
            user_id = cursor.lastrowid
            
            # 创建用户设置记录
            self.db.execute_query(
                """INSERT INTO user_settings (user_id, alert_enabled, anger_threshold, sad_threshold, 
                   camera_index, auto_save_screenshots, screenshot_interval)
                   VALUES (%s, 1, 0.85, 0.80, 0, 0, 60)""",
                (user_id,)
            )
            
            return True, "注册成功"
        except Error as e:
            print(f"注册用户错误: {e}")
            return False, "注册失败，请重试"
    
    def login_user(self, username, password):
        """用户登录"""
        password_hash = self.hash_password(password)
        
        try:
            cursor = self.db.execute_query(
                "SELECT id FROM users WHERE username = %s AND password_hash = %s",
                (username, password_hash)
            )
            
            user = cursor.fetchone() if cursor else None
            if user:
                self.current_user = username
                self.current_user_id = user[0]
                return True, "登录成功"
            else:
                return False, "用户名或密码错误"
        except Error as e:
            print(f"用户登录错误: {e}")
            return False, "登录失败，请重试"
    
    def logout_user(self):
        """用户登出"""
        self.current_user = None
        self.current_user_id = None
        return True
    
    def get_current_user(self):
        """获取当前用户信息"""
        return self.current_user, self.current_user_id
    
    def get_user_settings(self):
        """获取当前用户的设置"""
        if not self.current_user_id:
            return None
        
        try:
            cursor = self.db.execute_query(
                """SELECT alert_enabled, anger_threshold, sad_threshold, camera_index, 
                   auto_save_screenshots, screenshot_interval
                   FROM user_settings WHERE user_id = %s""",
                (self.current_user_id,)
            )
            
            settings = cursor.fetchone()
            if settings:
                return {
                    "alert_enabled": bool(settings[0]),
                    "alert_thresholds": {
                        "生气": settings[1],
                        "悲伤": settings[2]
                    },
                    "camera_index": settings[3],
                    "auto_save_screenshots": bool(settings[4]),
                    "screenshot_interval": settings[5]
                }
            return None
        except Error as e:
            print(f"获取用户设置错误: {e}")
            return None
    
    def save_user_settings(self, settings):
        """保存用户设置"""
        if not self.current_user_id:
            return False
        
        try:
            self.db.execute_query(
                """UPDATE user_settings 
                   SET alert_enabled = %s, 
                       anger_threshold = %s, 
                       sad_threshold = %s, 
                       camera_index = %s, 
                       auto_save_screenshots = %s, 
                       screenshot_interval = %s
                   WHERE user_id = %s""",
                (
                    settings["alert_enabled"],
                    settings["alert_thresholds"]["生气"],
                    settings["alert_thresholds"]["悲伤"],
                    settings["camera_index"],
                    settings["auto_save_screenshots"],
                    settings["screenshot_interval"],
                    self.current_user_id
                )
            )
            return True
        except Error as e:
            print(f"保存用户设置错误: {e}")
            return False
    
    def save_emotion_history(self, timestamp, emotion, probabilities):
        """保存情绪历史记录"""
        if not self.current_user_id:
            return False
        
        try:
            self.db.execute_query(
                """INSERT INTO emotion_history (user_id, timestamp, emotion, anger, happy, sad)
                   VALUES (%s, %s, %s, %s, %s, %s)""",
                (
                    self.current_user_id,
                    timestamp,
                    emotion,
                    probabilities[0],
                    probabilities[1],
                    probabilities[2]
                )
            )
            return True
        except Error as e:
            print(f"保存情绪历史错误: {e}")
            return False
    
    def save_screenshot(self, file_path, timestamp, emotion):
        """保存截图记录"""
        if not self.current_user_id:
            return False
        
        try:
            self.db.execute_query(
                """INSERT INTO screenshots (user_id, file_path, timestamp, emotion)
                   VALUES (%s, %s, %s, %s)""",
                (
                    self.current_user_id,
                    file_path,
                    timestamp,
                    emotion
                )
            )
            return True
        except Error as e:
            print(f"保存截图记录错误: {e}")
            return False
    
    def get_emotion_history(self):
        """获取当前用户的情绪历史记录"""
        if not self.current_user_id:
            return []
        
        try:
            cursor = self.db.execute_query(
                """SELECT timestamp, emotion, anger, happy, sad 
                   FROM emotion_history 
                   WHERE user_id = %s 
                   ORDER BY timestamp DESC""",
                (self.current_user_id,)
            )
            
            return cursor.fetchall() if cursor else []
        except Error as e:
            print(f"获取情绪历史错误: {e}")
            return []
    
    def get_screenshots(self):
        """获取当前用户的截图记录"""
        if not self.current_user_id:
            return []
        
        try:
            cursor = self.db.execute_query(
                """SELECT file_path, timestamp, emotion 
                   FROM screenshots 
                   WHERE user_id = %s 
                   ORDER BY timestamp DESC""",
                (self.current_user_id,)
            )
            
            return cursor.fetchall() if cursor else []
        except Error as e:
            print(f"获取截图记录错误: {e}")
            return []

# 登录UI类
class LoginUI:
    def __init__(self, master, user_manager, main_app):
        self.master = master
        self.user_manager = user_manager
        self.main_app = main_app  # 这里的 main_app 应该是 EmotionUI 的实例
        
        # 创建登录界面
        self.create_login_ui()
    
    def create_login_ui(self):
        """创建登录界面"""
        # 清空主窗口
        for widget in self.master.winfo_children():
            widget.destroy()
        
        # 设置窗口标题和大小
        self.master.title("情绪识别系统 - 登录")
        self.master.geometry("400x300")
        self.master.resizable(False, False)
        
        # 创建标题
        title_frame = ttk.Frame(self.master)
        title_frame.pack(pady=20)
        
        ttk.Label(title_frame, text="情绪识别系统", font=('微软雅黑', 20, 'bold')).pack()
        
        # 创建登录表单
        form_frame = ttk.Frame(self.master)
        form_frame.pack(pady=20)
        
        # 用户名
        ttk.Label(form_frame, text="用户名:", font=('微软雅黑', 12)).grid(row=0, column=0, sticky=tk.W, pady=10)
        self.username_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.username_var, width=25, font=('微软雅黑', 12)).grid(row=0, column=1, pady=10)
        
        # 密码
        ttk.Label(form_frame, text="密码:", font=('微软雅黑', 12)).grid(row=1, column=0, sticky=tk.W, pady=10)
        self.password_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.password_var, show="*", width=25, font=('微软雅黑', 12)).grid(row=1, column=1, pady=10)
        
        # 登录按钮
        button_frame = ttk.Frame(self.master)
        button_frame.pack(pady=20)
        
        ttk.Button(button_frame, text="登录", command=self.login, width=15, style='Large.TButton').pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="注册", command=self.show_register, width=15, style='Large.TButton').pack(side=tk.LEFT, padx=10)
        
        # 状态标签 - 使用红色文本显示错误信息
        self.status_var = tk.StringVar()
        self.status_label = ttk.Label(self.master, textvariable=self.status_var, foreground="red")
        self.status_label.pack(pady=10)
    
    def login(self):
        """处理登录"""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        
        if not username or not password:
            self.status_var.set("请输入用户名和密码")
            return
        
        # 调用用户管理器进行登录验证
        success, message = self.user_manager.login_user(username, password)
        
        if success:
            # 登录成功，显示成功消息并跳转到主界面
            self.status_var.set("登录成功，正在跳转...")
            self.master.update_idletasks()
            
            messagebox.showinfo("登录成功", message)
            
            # 确保 main_app 是 EmotionUI 的实例，并调用其初始化方法
            if isinstance(self.main_app, EmotionUI):
                self.main_app.setup_after_login()
            else:
                print("Error: main_app is not an instance of EmotionUI")
        else:
            # 登录失败，显示错误消息
            self.status_var.set(message)
            messagebox.showerror("登录失败", message)  # 添加消息框
            print(f"登录失败: {message}")  # 同时打印日志，方便调试
    
    def show_register(self):
        """显示注册界面"""
        RegisterUI(self.master, self.user_manager, self)

# 注册UI类
class RegisterUI:
    def __init__(self, master, user_manager, login_ui):
        self.master = master
        self.user_manager = user_manager
        self.login_ui = login_ui
        
        # 创建注册界面
        self.create_register_ui()
    
    def create_register_ui(self):
        """创建注册界面"""
        # 清空主窗口
        for widget in self.master.winfo_children():
            widget.destroy()
        
        # 设置窗口标题和大小
        self.master.title("情绪识别系统 - 注册")
        self.master.geometry("400x350")
        self.master.resizable(False, False)
        
        # 创建标题
        title_frame = ttk.Frame(self.master)
        title_frame.pack(pady=20)
        
        ttk.Label(title_frame, text="创建新账户", font=('微软雅黑', 20, 'bold')).pack()
        
        # 创建注册表单
        form_frame = ttk.Frame(self.master)
        form_frame.pack(pady=20)
        
        # 用户名
        ttk.Label(form_frame, text="用户名:", font=('微软雅黑', 12)).grid(row=0, column=0, sticky=tk.W, pady=10)
        self.username_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.username_var, width=25, font=('微软雅黑', 12)).grid(row=0, column=1, pady=10)
        
        # 密码
        ttk.Label(form_frame, text="密码:", font=('微软雅黑', 12)).grid(row=1, column=0, sticky=tk.W, pady=10)
        self.password_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.password_var, show="*", width=25, font=('微软雅黑', 12)).grid(row=1, column=1, pady=10)
        
        # 确认密码
        ttk.Label(form_frame, text="确认密码:", font=('微软雅黑', 12)).grid(row=2, column=0, sticky=tk.W, pady=10)
        self.confirm_password_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.confirm_password_var, show="*", width=25, font=('微软雅黑', 12)).grid(row=2, column=1, pady=10)
        
        # 注册按钮
        button_frame = ttk.Frame(self.master)
        button_frame.pack(pady=20)
        
        ttk.Button(button_frame, text="注册", command=self.register, width=15, style='Large.TButton').pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="返回登录", command=self.back_to_login, width=15, style='Large.TButton').pack(side=tk.LEFT, padx=10)
        
        # 状态标签
        self.status_var = tk.StringVar()
        ttk.Label(self.master, textvariable=self.status_var, foreground="red").pack(pady=10)
    
    def register(self):
        """处理注册"""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        confirm_password = self.confirm_password_var.get()
        
        if not username or not password:
            self.status_var.set("请输入用户名和密码")
            return
        
        if password != confirm_password:
            self.status_var.set("两次输入的密码不一致")
            return
        
        success, message = self.user_manager.register_user(username, password)
        if success:
            messagebox.showinfo("注册成功", "账户创建成功，请登录")
            self.back_to_login()
        else:
            self.status_var.set(message)
    
    def back_to_login(self):
        """返回登录界面"""
        self.login_ui.create_login_ui()

# 情绪识别主UI类
class EmotionUI:
    def __init__(self, master, user_manager):
        self.master = master
        self.user_manager = user_manager
        self.db = user_manager.db
        
        # 初始化运行状态
        self.is_running = False
        self.probabilities = [0.0] * len(categories)
        
        # 初始化摄像头引用
        self.cap = None
        
        # 初始化界面框架
        self.frames = {}
        
        # 初始化情绪历史记录
        self.emotion_history = []
        self.emotion_timestamps = []
        
        # 初始化设置
        self.settings = {}
        
        # 创建数据目录
        self.data_dir = Path("emotion_data")
        self.data_dir.mkdir(exist_ok=True)
        self.screenshots_dir = self.data_dir / "screenshots"
        self.screenshots_dir.mkdir(exist_ok=True)
        
        # 设置主题
        self.setup_theme()
    
    def setup_after_login(self):
        """登录成功后进行初始化设置"""
        # 加载用户设置
        self.settings = self.user_manager.get_user_settings() or {
            "alert_enabled": True,
            "alert_thresholds": {
                "生气": 0.85,
                "悲伤": 0.80
            },
            "camera_index": 0,
            "auto_save_screenshots": False,
            "screenshot_interval": 60
        }
        
        # 创建所有需要的界面框架
        self.create_main_menu()
        self.create_detection_ui()
        self.create_settings_ui()
        self.create_history_ui()
        
        # 显示主菜单
        self.show_main_menu()
    
    def setup_theme(self):
        """设置应用主题和样式"""
        style = ttk.Style()
        
        # 尝试使用更现代的主题
        try:
            style.theme_use('clam')
        except:
            pass
        
        # 配置各种样式
        style.configure('TFrame', background='#f0f0f0')
        style.configure('TLabel', background='#f0f0f0', font=('微软雅黑', 10))
        style.configure('TButton', font=('微软雅黑', 10))
        style.configure('Large.TButton', font=('微软雅黑', 14))
        style.configure('Title.TLabel', font=('微软雅黑', 24, 'bold'), background='#f0f0f0')
        style.configure('Subtitle.TLabel', font=('微软雅黑', 14), background='#f0f0f0')
        style.configure('Status.TLabel', font=('微软雅黑', 10), background='#e0e0e0')
        
        # 配置特殊按钮样式
        style.configure('Green.TButton', background='#4CAF50')
        style.configure('Red.TButton', background='#FF5252')
        style.configure('Blue.TButton', background='#2196F3')
        
        # 设置窗口背景色
        self.master.configure(background='#f0f0f0')
    
    def create_main_menu(self):
        """创建主菜单界面"""
        # 检查框架是否存在，如果不存在则创建
        if 'main_menu' not in self.frames:
            self.frames['main_menu'] = ttk.Frame(self.master)
        
        # 获取主菜单框架
        main_menu = self.frames['main_menu']
        
        # 清空主菜单框架（使用try-except避免bad window path错误）
        try:
            for widget in main_menu.winfo_children():
                widget.destroy()
        except _tkinter.TclError:
            # 如果窗口已被销毁，忽略错误
            pass
        
        # 设置主菜单样式
        main_menu.configure(padding="20")
        
        # 获取当前用户名
        current_user, _ = self.user_manager.get_current_user()
        
        # 创建标题和副标题
        title_frame = ttk.Frame(main_menu)
        title_frame.pack(fill=tk.X, pady=20)
        
        ttk.Label(title_frame, text="情绪识别系统", style='Title.TLabel').pack()
        ttk.Label(title_frame, text=f"当前用户: {current_user}", style='Subtitle.TLabel').pack(pady=5)
        
        # 创建功能按钮区域
        button_frame = ttk.Frame(main_menu)
        button_frame.pack(pady=30)
        
        # 创建主要功能按钮
        btn_width = 25
        btn_padding = 15
        
        # 开始检测按钮（绿色）
        start_btn = ttk.Button(button_frame, text="开始情绪检测",
                              command=self.start_detection,
                              width=btn_width, style='Large.TButton')
        start_btn.grid(row=0, column=0, padx=btn_padding, pady=btn_padding, sticky='ew')
        
        # 历史记录按钮（蓝色）
        history_btn = ttk.Button(button_frame, text="查看情绪历史",
                                command=self.show_history,
                                width=btn_width, style='Large.TButton')
        history_btn.grid(row=0, column=1, padx=btn_padding, pady=btn_padding, sticky='ew')
        
        # 设置按钮
        settings_btn = ttk.Button(button_frame, text="系统设置",
                                 command=self.show_settings,
                                 width=btn_width, style='Large.TButton')
        settings_btn.grid(row=1, column=0, padx=btn_padding, pady=btn_padding, sticky='ew')
        
        # 切换用户按钮
        switch_btn = ttk.Button(button_frame, text="切换用户",
                               command=self.switch_user,
                               width=btn_width, style='Large.TButton')
        switch_btn.grid(row=1, column=1, padx=btn_padding, pady=btn_padding, sticky='ew')
        
        # 添加打开文件夹按钮
        open_folder_btn = ttk.Button(button_frame, text="打开文件夹识别照片",
                                     command=self.open_folder_and_recognize,
                                     width=btn_width, style='Large.TButton')
        open_folder_btn.grid(row=2, column=0, padx=btn_padding, pady=btn_padding, sticky='ew')
        
        # 添加退出按钮
        exit_btn = ttk.Button(button_frame, text="退出系统",
                             command=self.safe_exit,
                             width=btn_width, style='Large.TButton')
        exit_btn.grid(row=2, column=1, padx=btn_padding, pady=btn_padding, sticky='ew')
        
        # 添加功能描述区域
        desc_frame = ttk.Frame(main_menu)
        desc_frame.pack(fill=tk.X, pady=20)
        
        features = [
            "✓ 实时情绪识别与分析",
            "✓ 情绪历史记录与趋势分析",
            "✓ 极端情绪状态预警",
            "✓ 截图与数据导出功能"
        ]
        
        for feature in features:
            ttk.Label(desc_frame, text=feature, font=('微软雅黑', 12)).pack(anchor=tk.W, pady=3)
        
        # 版本信息
        footer_frame = ttk.Frame(main_menu)
        footer_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=10)
        
        ttk.Label(footer_frame, text="Version 2.0", foreground="gray",
                 font=('微软雅黑', 10)).pack(side=tk.RIGHT)
        ttk.Label(footer_frame, text="© 2025 情绪识别系统", foreground="gray",
                 font=('微软雅黑', 10)).pack(side=tk.LEFT)
    
    def create_detection_ui(self):
        """创建检测界面"""
        # 检查框架是否存在，如果不存在则创建
        if 'detection_ui' not in self.frames:
            self.frames['detection_ui'] = ttk.Frame(self.master)
        
        # 获取检测界面框架
        detection_ui = self.frames['detection_ui']
        
        # 清空检测界面框架（使用try-except避免bad window path错误）
        try:
            for widget in detection_ui.winfo_children():
                widget.destroy()
        except _tkinter.TclError:
            # 如果窗口已被销毁，忽略错误
            pass
        
        # 创建主布局
        main_paned = ttk.PanedWindow(detection_ui, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧面板 - 视频和控制
        left_frame = ttk.Frame(main_paned, padding=5)
        main_paned.add(left_frame, weight=3)
        
        # 右侧面板 - 情绪分析和建议
        right_frame = ttk.Frame(main_paned, padding=5)
        main_paned.add(right_frame, weight=2)
        
        # === 左侧面板内容 ===
        # 标题
        ttk.Label(left_frame, text="实时情绪检测", style='Title.TLabel').pack(pady=(0, 10))
        
        # 视频显示区域
        video_frame = ttk.Frame(left_frame, borderwidth=2, relief=tk.GROOVE, padding=5)
        video_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.video_label = ttk.Label(video_frame)
        self.video_label.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 控制按钮区域
        control_frame = ttk.Frame(left_frame)
        control_frame.pack(fill=tk.X, pady=10)
        
        # 按钮行1
        btn_frame1 = ttk.Frame(control_frame)
        btn_frame1.pack(fill=tk.X, pady=5)
        
        self.btn_toggle = ttk.Button(btn_frame1, text="停止检测",
                                    command=self.toggle_detection,
                                    style='Large.TButton')
        self.btn_toggle.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        self.btn_screenshot = ttk.Button(btn_frame1, text="保存截图",
                                       command=self.save_screenshot,
                                       style='Large.TButton')
        self.btn_screenshot.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # 按钮行2
        btn_frame2 = ttk.Frame(control_frame)
        btn_frame2.pack(fill=tk.X, pady=5)
        
        self.btn_history = ttk.Button(btn_frame2, text="历史记录",
                                     command=self.show_history,
                                     style='Large.TButton')
        self.btn_history.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        self.btn_back = ttk.Button(btn_frame2, text="返回菜单",
                                  command=self.show_main_menu,
                                  style='Large.TButton')
        self.btn_back.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # === 右侧面板内容 ===
        # 情绪分析标题
        ttk.Label(right_frame, text="情绪分析结果", style='Title.TLabel').pack(pady=(0, 10))
        
        # 当前情绪显示
        emotion_frame = ttk.Frame(right_frame, borderwidth=2, relief=tk.GROOVE, padding=10)
        emotion_frame.pack(fill=tk.X, pady=5)
        
        self.current_emotion_label = ttk.Label(emotion_frame, text="等待检测...",
                                             font=('微软雅黑', 16, 'bold'))
        self.current_emotion_label.pack(pady=5)
        
        # 使用文本图标代替emoji
        self.emotion_icon_label = ttk.Label(emotion_frame, font=('微软雅黑', 32))
        self.emotion_icon_label.pack(pady=5)
        
        # 情绪概率条形图
        chart_frame = ttk.Frame(right_frame, borderwidth=2, relief=tk.GROOVE, padding=5)
        chart_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        ttk.Label(chart_frame, text="情绪概率分布", font=('微软雅黑', 14, 'bold')).pack(pady=5)
        
        # 使用matplotlib创建更美观的图表
        self.fig = Figure(figsize=(5, 3), dpi=100)
        self.ax = self.fig.add_subplot(111)
        self.canvas = FigureCanvasTkAgg(self.fig, master=chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 初始化图表
        self.update_chart([0] * len(categories))
        
        # 警报区域
        alert_frame = ttk.Frame(right_frame, borderwidth=2, relief=tk.GROOVE, padding=5)
        alert_frame.pack(fill=tk.X, pady=5)
        
        self.alert_var = tk.StringVar(value="")
        self.alert_label = ttk.Label(alert_frame, textvariable=self.alert_var,
                                    font=('微软雅黑', 12))
        self.alert_label.pack(fill=tk.X, pady=5)
        
        # 状态栏
        self.status = ttk.Label(detection_ui, relief=tk.SUNKEN, anchor=tk.W,
                               style='Status.TLabel')
        self.status.pack(side=tk.BOTTOM, fill=tk.X)
    
    def update_chart(self, probabilities):
        """使用matplotlib更新情绪概率图表"""
        self.ax.clear()
        
        # 设置条形图
        bars = self.ax.bar(categories, probabilities, color=[emotion_colors.get(c, '#607D8B') for c in categories])
        
        # 在条形上方显示概率值
        for bar, prob in zip(bars, probabilities):
            height = bar.get_height()
            self.ax.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                        f'{prob:.2f}', ha='center', va='bottom', fontsize=9)
        
        # 设置图表样式
        self.ax.set_ylim(0, 1.0)
        self.ax.set_ylabel('概率')
        self.ax.set_title('情绪概率分布')
        self.fig.tight_layout()
        
        # 刷新画布
        self.canvas.draw()
    
    def create_settings_ui(self):
        """创建设置界面"""
        # 确保框架存在
        if 'settings_ui' not in self.frames:
            self.frames['settings_ui'] = ttk.Frame(self.master)
        
        # 清空设置界面框架
        for widget in self.frames['settings_ui'].winfo_children():
            widget.destroy()
        
        # 使用 self.frames['settings_ui'] 代替 self.settings_ui
        settings_ui = self.frames['settings_ui']
        
        # 创建主布局
        main_frame = ttk.Frame(settings_ui, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        ttk.Label(main_frame, text="系统设置", style='Title.TLabel').pack(pady=(0, 20))
        
        # 创建设置区域
        settings_notebook = ttk.Notebook(main_frame)
        settings_notebook.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 警报设置页
        alert_frame = ttk.Frame(settings_notebook, padding="10")
        settings_notebook.add(alert_frame, text="警报设置")
        
        # 摄像头设置页
        camera_frame = ttk.Frame(settings_notebook, padding="10")
        settings_notebook.add(camera_frame, text="摄像头设置")
        
        # 截图设置页
        screenshot_frame = ttk.Frame(settings_notebook, padding="10")
        settings_notebook.add(screenshot_frame, text="截图设置")
        
        # 其他设置页
        other_frame = ttk.Frame(settings_notebook, padding="10")
        settings_notebook.add(other_frame, text="其他设置")
        
        # === 警报设置页 ===
        # 启用警报
        self.alert_enabled_var = tk.BooleanVar(value=self.settings.get("alert_enabled", True))
        alert_enabled_check = ttk.Checkbutton(alert_frame, text="启用情绪异常警报",
                                            variable=self.alert_enabled_var)
        alert_enabled_check.pack(anchor=tk.W, pady=5)
        
        # 生气阈值
        anger_frame = ttk.Frame(alert_frame)
        anger_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(anger_frame, text="生气阈值:").pack(side=tk.LEFT, padx=5)
        
        self.anger_threshold_var = tk.DoubleVar(value=self.settings.get("alert_thresholds", {}).get("生气", 0.85))
        
        # 自定义分辨率的 Scale 组件
        anger_scale = ttk.Scale(anger_frame, from_=0.0, to=1.0,
                              variable=self.anger_threshold_var, orient=tk.HORIZONTAL,
                              length=300)
        anger_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        self.anger_threshold_label = ttk.Label(anger_frame, text=f"{self.anger_threshold_var.get():.2f}")
        self.anger_threshold_label.pack(side=tk.LEFT, padx=5)
        
        def snap_to_resolution(value, resolution=0.05):
            """将值舍入到指定分辨率"""
            return round(value / resolution) * resolution
        
        def update_anger_threshold_label(event):
            # 获取当前值并应用分辨率
            value = self.anger_threshold_var.get()
            snapped_value = snap_to_resolution(value)
            self.anger_threshold_var.set(snapped_value)
            self.anger_threshold_label.config(text=f"{snapped_value:.2f}")
        
        anger_scale.bind("<Motion>", update_anger_threshold_label)
        anger_scale.bind("<ButtonRelease-1>", update_anger_threshold_label)
        
        # 悲伤阈值
        sad_frame = ttk.Frame(alert_frame)
        sad_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(sad_frame, text="悲伤阈值:").pack(side=tk.LEFT, padx=5)
        
        self.sad_threshold_var = tk.DoubleVar(value=self.settings.get("alert_thresholds", {}).get("悲伤", 0.80))
        
        # 自定义分辨率的 Scale 组件
        sad_scale = ttk.Scale(sad_frame, from_=0.0, to=1.0,
                            variable=self.sad_threshold_var, orient=tk.HORIZONTAL,
                            length=300)
        sad_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        self.sad_threshold_label = ttk.Label(sad_frame, text=f"{self.sad_threshold_var.get():.2f}")
        self.sad_threshold_label.pack(side=tk.LEFT, padx=5)
        
        def update_sad_threshold_label(event):
            # 获取当前值并应用分辨率
            value = self.sad_threshold_var.get()
            snapped_value = snap_to_resolution(value)
            self.sad_threshold_var.set(snapped_value)
            self.sad_threshold_label.config(text=f"{snapped_value:.2f}")
        
        sad_scale.bind("<Motion>", update_sad_threshold_label)
        sad_scale.bind("<ButtonRelease-1>", update_sad_threshold_label)
        
        # === 摄像头设置页 ===
        # 摄像头选择
        camera_frame_inner = ttk.Frame(camera_frame)
        camera_frame_inner.pack(fill=tk.X, pady=10)
        
        ttk.Label(camera_frame_inner, text="摄像头选择:").pack(side=tk.LEFT, padx=5)
        
        self.camera_index_var = tk.IntVar(value=self.settings.get("camera_index", 0))
        camera_combobox = ttk.Combobox(camera_frame_inner, textvariable=self.camera_index_var,
                                      values=[0, 1, 2, 3], width=5)
        camera_combobox.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(camera_frame_inner, text="测试摄像头", command=self.test_camera).pack(side=tk.LEFT, padx=5)
        
        # === 截图设置页 ===
        # 自动保存截图
        self.auto_save_var = tk.BooleanVar(value=self.settings.get("auto_save_screenshots", False))
        auto_save_check = ttk.Checkbutton(screenshot_frame, text="自动保存情绪变化截图",
                                        variable=self.auto_save_var)
        auto_save_check.pack(anchor=tk.W, pady=5)
        
        # 截图间隔
        interval_frame = ttk.Frame(screenshot_frame)
        interval_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(interval_frame, text="截图最小间隔 (秒):").pack(side=tk.LEFT, padx=5)
        
        self.interval_var = tk.IntVar(value=self.settings.get("screenshot_interval", 60))
        interval_spinbox = ttk.Spinbox(interval_frame, from_=10, to=300, increment=10,
                                      textvariable=self.interval_var, width=10)
        interval_spinbox.pack(side=tk.LEFT, padx=5)
        
        # === 其他设置页 ===
        # 界面主题
        theme_frame = ttk.Frame(other_frame)
        theme_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(theme_frame, text="界面主题:").pack(side=tk.LEFT, padx=5)
        
        theme_var = tk.StringVar(value="light")
        theme_combobox = ttk.Combobox(theme_frame, textvariable=theme_var,
                                     values=["light", "dark"], width=10)
        theme_combobox.pack(side=tk.LEFT, padx=5)
        
        # 底部按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)
        
        ttk.Button(button_frame, text="保存设置", command=self.save_settings,
                style='Large.TButton').pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="返回菜单", command=self.show_main_menu,
                style='Large.TButton').pack(side=tk.RIGHT, padx=5)
        
        # 状态栏
        self.status = ttk.Label(settings_ui, relief=tk.SUNKEN, anchor=tk.W,
                               style='Status.TLabel')
        self.status.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_history_ui(self):
        """创建历史记录界面"""
        # 确保框架存在
        if 'history_ui' not in self.frames:
            self.frames['history_ui'] = ttk.Frame(self.master)
        
        # 清空历史界面框架
        for widget in self.frames['history_ui'].winfo_children():
            widget.destroy()
        
        # 使用 self.frames['history_ui'] 代替 self.history_ui
        history_ui = self.frames['history_ui']
        
        # 创建主布局
        main_frame = ttk.Frame(history_ui, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        ttk.Label(main_frame, text="情绪历史记录", style='Title.TLabel').pack(pady=(0, 20))
        
        # 创建历史记录区域
        history_frame = ttk.LabelFrame(main_frame, text="历史记录", padding="10")
        history_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建左侧列表框和右侧详情面板
        paned_window = ttk.PanedWindow(history_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧列表框
        list_frame = ttk.Frame(paned_window)
        paned_window.add(list_frame, weight=1)
        
        # 创建滚动条
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建列表框
        self.history_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set,
                                        font=('微软雅黑', 12), selectmode=tk.SINGLE)
        self.history_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.history_listbox.yview)
        
        # 绑定选择事件
        self.history_listbox.bind('<<ListboxSelect>>', self.on_history_select)
        
        # 右侧详情面板
        detail_frame = ttk.Frame(paned_window)
        paned_window.add(detail_frame, weight=1)
        
        # 创建详情文本框
        self.detail_text = tk.Text(detail_frame, wrap=tk.WORD, font=('微软雅黑', 12),
                                height=10, state=tk.DISABLED)
        self.detail_text.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建图表区域
        chart_frame = ttk.LabelFrame(main_frame, text="情绪趋势分析", padding="10")
        chart_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 使用matplotlib创建趋势图
        self.trend_fig = Figure(figsize=(10, 4), dpi=100)
        self.trend_ax = self.trend_fig.add_subplot(111)
        self.trend_canvas = FigureCanvasTkAgg(self.trend_fig, master=chart_frame)
        self.trend_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 底部按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)
        
        ttk.Button(button_frame, text="刷新历史记录", command=self.load_history_data,
                style='Large.TButton').pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="查看截图", command=self.view_screenshots,
                style='Large.TButton').pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="导出数据", command=self.export_history_data,
                style='Large.TButton').pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="清除所有数据", command=self.clear_all_data,
                style='Large.TButton').pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="返回菜单", command=self.show_main_menu,
                style='Large.TButton').pack(side=tk.RIGHT, padx=5)
        
        # 状态栏
        self.status = ttk.Label(history_ui, relief=tk.SUNKEN, anchor=tk.W,
                               style='Status.TLabel')
        self.status.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 加载历史数据
        self.load_history_data()
    
    def update_trend_chart(self):
        """更新情绪趋势图"""
        self.trend_ax.clear()
        
        if not self.emotion_history:
            self.trend_ax.set_title("无历史数据")
            self.trend_canvas.draw()
            return
        
        # 只显示最近的20个数据点，避免图表过于拥挤
        recent_history = self.emotion_history[-20:]
        recent_timestamps = self.emotion_timestamps[-20:]
        
        # 转换时间戳为可显示的格式
        time_labels = [ts.strftime("%H:%M:%S") for ts in recent_timestamps]
        
        # 绘制每个情绪的趋势线
        for i, emotion in enumerate(categories):
            self.trend_ax.plot(time_labels, [data[i] for data in recent_history], 
                             label=emotion, color=emotion_colors.get(emotion, '#607D8B'))
        
        # 设置图表样式
        self.trend_ax.set_xlabel('时间')
        self.trend_ax.set_ylabel('概率')
        self.trend_ax.set_title('情绪变化趋势')
        self.trend_ax.legend()
        self.trend_fig.autofmt_xdate()  # 自动旋转x轴标签避免重叠
        self.trend_fig.tight_layout()
        
        # 刷新画布
        self.trend_canvas.draw()
    
    def start_detection(self):
        """开始检测"""
        # 隐藏其他界面
        self.main_menu.pack_forget()
        self.settings_ui.pack_forget()
        self.history_ui.pack_forget()
        
        # 显示检测界面
        self.detection_ui.pack(fill=tk.BOTH, expand=True)
        
        # 初始化摄像头
        self.init_camera()
        
        # 开始检测
        self.toggle_detection()
    
    def stop_detection(self):
        """停止检测"""
        self.is_running = False
        self.btn_toggle.config(text="开始检测")
        self.update_status("检测已停止")
    
    def show_main_menu(self):
        """显示主菜单"""
        # 停止检测
        self.is_running = False
        
        # 释放摄像头
        if self.cap:
            self.cap.release()
            self.cap = None
        
        # 隐藏所有界面框架
        for frame_name in self.frames:
            if frame_name != 'main_menu':
                self.frames[frame_name].pack_forget()
        
        # 显示主菜单
        self.frames['main_menu'].pack(fill=tk.BOTH, expand=True)
    
    def show_settings(self):
        """显示设置界面"""
        for frame_name in self.frames:
            if frame_name != 'settings_ui':
                self.frames[frame_name].pack_forget()
        self.frames['settings_ui'].pack(fill=tk.BOTH, expand=True)
    
    def show_history(self):
        """显示历史记录界面"""
        for frame_name in self.frames:
            if frame_name != 'history_ui':
                self.frames[frame_name].pack_forget()
        self.frames['history_ui'].pack(fill=tk.BOTH, expand=True)
    
    def init_camera(self):
        """初始化摄像头"""
        camera_index = self.settings.get("camera_index", 0)
        self.cap = cv2.VideoCapture(camera_index)
        
        if not self.cap.isOpened():
            # 尝试其他摄像头索引
            for i in range(5):
                if i != camera_index:
                    self.cap = cv2.VideoCapture(i)
                    if self.cap.isOpened():
                        self.settings["camera_index"] = i
                        self.camera_index_var.set(i)
                        messagebox.showinfo("摄像头检测", f"已自动切换到摄像头 {i}")
                        break
        
        if not self.cap.isOpened():
            messagebox.showerror("错误", "无法打开摄像头，请检查设备连接")
            self.show_main_menu()
        else:
            self.update_status("摄像头就绪 | 点击[开始检测]启动识别")
    
    def toggle_detection(self):
        """检测切换方法"""
        if not self.is_running:
            self.is_running = True
            self.btn_toggle.config(text="停止检测")
            threading.Thread(target=self.video_loop, daemon=True).start()
            self.update_status("情绪检测已启动")
        else:
            self.is_running = False
            self.btn_toggle.config(text="开始检测")
            self.update_status("情绪检测已停止")
    
    def video_loop(self):
        """视频处理循环"""
        last_screenshot_time = datetime.datetime.now()
        last_emotion = None
        
        while self.is_running:
            ret, frame = self.cap.read()
            if not ret:
                self.update_status("无法获取视频帧")
                break
            
            # 转换为RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # 检测人脸
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            faces = face_cascade.detectMultiScale(gray, 1.3, 5)
            
            # 处理检测到的人脸
            if len(faces) > 0:
                # 选择最大的人脸
                (x, y, w, h) = max(faces, key=lambda f: f[2] * f[3])
                
                # 提取人脸区域并调整大小
                face_roi = rgb_frame[y:y+h, x:x+w]
                face_img = Image.fromarray(face_roi).resize((48, 48))
                
                # 预处理图像
                img_np = np.array(face_img) / 255.0
                img_input = img_np.transpose((2, 0, 1))
                img_input = np.expand_dims(img_input, axis=0)
                img_input = Tensor(img_input, dtype=ms.float32)
                
                # 进行预测
                output = net(img_input)
                probabilities = nn.Softmax()(output).asnumpy()[0]
                
                # 更新UI
                self.probabilities = probabilities
                self.update_emotion_display()
                
                # 记录情绪历史
                now = datetime.datetime.now()
                self.emotion_history.append(probabilities)
                self.emotion_timestamps.append(now)
                
                # 保存到数据库
                main_emotion = categories[np.argmax(probabilities)]
                self.user_manager.save_emotion_history(now, main_emotion, probabilities)
                
                # 检测情绪变化并自动保存截图
                if (self.auto_save_var.get() and 
                    last_emotion is not None and 
                    main_emotion != last_emotion and 
                    now - last_screenshot_time > datetime.timedelta(seconds=self.interval_var.get())):
                    self.save_emotion_screenshot(frame, main_emotion)
                    last_screenshot_time = now
                
                last_emotion = main_emotion
                
                # 检查是否需要触发警报
                self.check_alerts(probabilities)
                
                # 在视频上绘制人脸框和情绪标签
                cv2.rectangle(frame, (x, y), (x+w, y+h), (0, 255, 0), 2)
                emotion_text = f"{main_emotion}: {probabilities[np.argmax(probabilities)]:.2f}"
                cv2.putText(frame, emotion_text, (x, y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
                                       # 显示处理后的帧
            img = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            img = Image.fromarray(img)
            img = ImageTk.PhotoImage(image=img)
            
            # 更新视频显示
            self.video_label.config(image=img)
            self.video_label.image = img
            
            # 限制处理速度
            self.master.after(30)
        else:
            # 如果没有检测到人脸，显示提示
            cv2.putText(frame, "未检测到人脸", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 0, 255), 2)
            
            # 显示处理后的帧
            img = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            img = Image.fromarray(img)
            img = ImageTk.PhotoImage(image=img)
            
            # 更新视频显示
            self.video_label.config(image=img)
            self.video_label.image = img
            
            # 清空情绪显示
            self.current_emotion_label.config(text="未检测到人脸")
            self.emotion_icon_label.config(text="")
            self.alert_var.set("")
            
            # 限制处理速度
            self.master.after(100)
    
    def update_emotion_display(self):
        """更新情绪显示"""
        if not self.probabilities:
            return
        
        # 获取最高概率的情绪
        max_idx = np.argmax(self.probabilities)
        main_emotion = categories[max_idx]
        confidence = self.probabilities[max_idx]
        
        # 更新情绪标签
        self.current_emotion_label.config(text=f"{main_emotion} ({confidence:.2f})")
        
        # 更新情绪图标
        self.emotion_icon_label.config(text=emotion_icons.get(main_emotion, ""))
        
        # 更新图表
        self.update_chart(self.probabilities)
    
    def check_alerts(self, probabilities):
        """检查是否需要触发警报"""
        if not self.alert_enabled_var.get():
            return
        
        anger_threshold = self.anger_threshold_var.get()
        sad_threshold = self.sad_threshold_var.get()
        
        # 检查生气情绪
        if probabilities[0] >= anger_threshold:
            self.alert_var.set(f"警告: 生气程度较高 ({probabilities[0]:.2f})")
            self.alert_label.config(foreground="#FF5252")  # 红色
            return
        
        # 检查悲伤情绪
        if probabilities[2] >= sad_threshold:
            self.alert_var.set(f"警告: 悲伤程度较高 ({probabilities[2]:.2f})")
            self.alert_label.config(foreground="#2196F3")  # 蓝色
            return
        
        # 如果没有警报，清空警报标签
        self.alert_var.set("")
    
    def save_screenshot(self):
        """手动保存当前帧"""
        if not hasattr(self, 'cap') or not self.cap or not self.is_running:
            messagebox.showinfo("提示", "请先启动情绪检测")
            return
        
        ret, frame = self.cap.read()
        if ret:
            # 获取当前主要情绪
            main_emotion = categories[np.argmax(self.probabilities)] if self.probabilities else "未知"
            
            # 保存截图
            self.save_emotion_screenshot(frame, main_emotion)
    
    def save_emotion_screenshot(self, frame, emotion):
        """保存带情绪信息的截图"""
        # 创建文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{emotion}.png"
        filepath = str(self.screenshots_dir / filename)
        
        # 保存图像
        cv2.imwrite(filepath, frame)
        
        # 保存到数据库
        self.user_manager.save_screenshot(filepath, datetime.datetime.now(), emotion)
        
        # 更新状态
        self.update_status(f"已保存截图: {filename}")
    
    def update_status(self, message):
        """更新状态栏消息"""
        self.status.config(text=message)
    
    def load_history_data(self):
        """加载历史数据"""
        # 清空列表
        self.history_listbox.delete(0, tk.END)
        self.emotion_history = []
        self.emotion_timestamps = []
        
        # 从数据库获取历史记录
        history_records = self.user_manager.get_emotion_history()
        
        for record in history_records:
            timestamp, emotion, anger, happy, sad = record
            timestamp = datetime.datetime.strptime(str(timestamp), "%Y-%m-%d %H:%M:%S")
            display_text = f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')} - {emotion} ({max(anger, happy, sad):.2f})"
            
            # 添加到列表
            self.history_listbox.insert(tk.END, display_text)
            
            # 保存数据用于图表
            self.emotion_history.append([anger, happy, sad])
            self.emotion_timestamps.append(timestamp)
        
        # 更新趋势图
        self.update_trend_chart()
    
    def on_history_select(self, event):
        """历史记录选择事件处理"""
        selection = self.history_listbox.curselection()
        if not selection:
            return
        
        index = selection[0]
        record = self.emotion_history[index]
        timestamp = self.emotion_timestamps[index]
        
        # 格式化详情文本
        detail_text = (f"时间: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                      f"情绪分析:\n"
                      f"生气: {record[0]:.2f}\n"
                      f"开心: {record[1]:.2f}\n"
                      f"悲伤: {record[2]:.2f}\n\n"
                      f"主要情绪: {categories[np.argmax(record)]}")
        
        # 更新详情文本
        self.detail_text.config(state=tk.NORMAL)
        self.detail_text.delete(1.0, tk.END)
        self.detail_text.insert(tk.END, detail_text)
        self.detail_text.config(state=tk.DISABLED)
    
    def view_screenshots(self):
        """查看截图"""
        screenshots = self.user_manager.get_screenshots()
        
        if not screenshots:
            messagebox.showinfo("提示", "没有找到截图记录")
            return
        
        # 创建截图查看窗口
        view_window = tk.Toplevel(self.master)
        view_window.title("情绪截图记录")
        view_window.geometry("800x600")
        
        # 创建主框架
        main_frame = ttk.Frame(view_window)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建左侧列表框
        list_frame = ttk.LabelFrame(main_frame, text="截图列表", padding=10)
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        screenshot_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set,
                                      font=('微软雅黑', 12))
        screenshot_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=screenshot_listbox.yview)
        
        # 创建右侧图像显示区域
        image_frame = ttk.LabelFrame(main_frame, text="截图预览", padding=10)
        image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        image_label = ttk.Label(image_frame)
        image_label.pack(fill=tk.BOTH, expand=True)
        
        # 创建信息标签
        info_label = ttk.Label(image_frame, text="选择一个截图查看详情", font=('微软雅黑', 12))
        info_label.pack(pady=10)
        
        # 填充列表框
        for i, (filepath, timestamp, emotion) in enumerate(screenshots):
            filename = os.path.basename(filepath)
            display_text = f"{timestamp} - {emotion} - {filename}"
            screenshot_listbox.insert(tk.END, display_text)
        
        # 绑定选择事件
        def on_screenshot_select(event):
            selection = screenshot_listbox.curselection()
            if not selection:
                return
            
            index = selection[0]
            filepath, timestamp, emotion = screenshots[index]
            
            # 更新信息标签
            info_label.config(text=f"{timestamp} - {emotion}")
            
            # 加载并显示图像
            try:
                img = Image.open(filepath)
                img.thumbnail((600, 400))  # 调整图像大小以适应窗口
                img = ImageTk.PhotoImage(img)
                image_label.config(image=img)
                image_label.image = img
            except Exception as e:
                image_label.config(text=f"无法加载图像: {str(e)}")
        
        screenshot_listbox.bind('<<ListboxSelect>>', on_screenshot_select)
    
    def save_settings(self):
        """保存用户设置"""
        self.settings = {
            "alert_enabled": self.alert_enabled_var.get(),
            "alert_thresholds": {
                "生气": self.anger_threshold_var.get(),
                "悲伤": self.sad_threshold_var.get()
            },
            "camera_index": self.camera_index_var.get(),
            "auto_save_screenshots": self.auto_save_var.get(),
            "screenshot_interval": self.interval_var.get()
        }
        
        # 保存到数据库
        if self.user_manager.save_user_settings(self.settings):
            messagebox.showinfo("成功", "设置已保存")
        else:
            messagebox.showerror("错误", "保存设置失败")
    
    def test_camera(self):
        """测试摄像头"""
        camera_index = self.camera_index_var.get()
        
        # 尝试打开摄像头
        test_cap = cv2.VideoCapture(camera_index)
        
        if test_cap.isOpened():
            # 读取一帧并显示
            ret, frame = test_cap.read()
            if ret:
                # 创建测试窗口
                test_window = tk.Toplevel(self.master)
                test_window.title(f"摄像头测试 (索引: {camera_index})")
                test_window.geometry("640x480")
                
                # 显示摄像头画面
                img = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                img = Image.fromarray(img)
                img = ImageTk.PhotoImage(image=img)
                
                label = ttk.Label(test_window, image=img)
                label.image = img
                label.pack(fill=tk.BOTH, expand=True)
                
                # 添加关闭按钮
                ttk.Button(test_window, text="关闭", command=test_window.destroy).pack(pady=10)
                
                messagebox.showinfo("成功", "摄像头测试成功")
            else:
                messagebox.showerror("错误", "无法获取摄像头画面")
            
            test_cap.release()
        else:
            messagebox.showerror("错误", f"无法打开摄像头索引 {camera_index}")
    
    def export_history_data(self):
        """导出历史数据"""
        if not self.emotion_history:
            messagebox.showinfo("提示", "没有历史数据可导出")
            return
        
        # 打开文件对话框
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")],
            title="保存情绪历史数据"
        )
        
        if not filename:
            return
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入表头
                writer.writerow(["时间", "主要情绪", "生气概率", "开心概率", "悲伤概率"])
                
                # 写入数据
                for i, record in enumerate(self.emotion_history):
                    timestamp = self.emotion_timestamps[i].strftime("%Y-%m-%d %H:%M:%S")
                    main_emotion = categories[np.argmax(record)]
                    writer.writerow([timestamp, main_emotion, record[0], record[1], record[2]])
            
            messagebox.showinfo("成功", f"数据已导出到 {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"导出数据失败: {str(e)}")
    
    def clear_all_data(self):
        """清除所有数据"""
        if messagebox.askyesno("确认", "确定要清除所有情绪历史和截图数据吗？此操作不可恢复！"):
            try:
                # 删除数据库中的数据
                user_id = self.user_manager.current_user_id
                if user_id:
                    # 删除截图文件
                    screenshots = self.user_manager.get_screenshots()
                    for filepath, _, _ in screenshots:
                        try:
                            if os.path.exists(filepath):
                                os.remove(filepath)
                        except:
                            pass
                    
                    # 删除数据库记录
                    self.db.execute_query("DELETE FROM emotion_history WHERE user_id = %s", (user_id,))
                    self.db.execute_query("DELETE FROM screenshots WHERE user_id = %s", (user_id,))
                    
                    # 刷新UI
                    self.load_history_data()
                    messagebox.showinfo("成功", "所有数据已清除")
            except Exception as e:
                messagebox.showerror("错误", f"清除数据失败: {str(e)}")
    
    def switch_user(self):
        """切换用户"""
        # 停止检测
        self.is_running = False
        
        # 释放摄像头
        if self.cap:
            self.cap.release()
            self.cap = None
        
        # 用户登出
        self.user_manager.logout_user()
        
        # 显示登录界面
        LoginUI(self.master, self.user_manager, self)
    
    def safe_exit(self):
        """安全退出应用程序"""
        # 停止检测
        self.is_running = False
        
        # 释放摄像头
        if self.cap:
            self.cap.release()
            self.cap = None
        
        # 断开数据库连接
        self.db.disconnect()
        
        # 退出应用
        self.master.destroy()
    
    def open_folder_and_recognize(self):
        """打开文件夹并识别照片"""
        folder_path = filedialog.askdirectory(title="选择包含人脸照片的文件夹")
        
        if not folder_path:
            return
        
        # 创建结果窗口
        result_window = tk.Toplevel(self.master)
        result_window.title("批量情绪识别结果")
        result_window.geometry("800x600")
        
        # 创建主框架
        main_frame = ttk.Frame(result_window)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建左侧列表框
        list_frame = ttk.LabelFrame(main_frame, text="图片列表", padding=10)
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        image_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set,
                                  font=('微软雅黑', 12))
        image_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=image_listbox.yview)
        
        # 创建右侧结果区域
        result_frame = ttk.LabelFrame(main_frame, text="识别结果", padding=10)
        result_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        image_label = ttk.Label(result_frame)
        image_label.pack(fill=tk.BOTH, expand=True, pady=10)
        
        result_text = tk.Text(result_frame, height=10, width=40, wrap=tk.WORD,
                            font=('微软雅黑', 12))
        result_text.pack(fill=tk.X, pady=10)
        result_text.config(state=tk.DISABLED)
        
        # 获取文件夹中的所有图片
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        image_files = []
        
        for file in os.listdir(folder_path):
            if any(file.lower().endswith(ext) for ext in image_extensions):
                image_files.append(file)
                image_listbox.insert(tk.END, file)
        
        if not image_files:
            messagebox.showinfo("提示", "所选文件夹中没有找到图片文件")
            result_window.destroy()
            return
        
        # 处理图片并保存结果
        results = {}
        
        def process_image(image_path):
            """处理单张图片"""
            try:
                # 读取图片
                img = cv2.imread(image_path)
                if img is None:
                    return None
                
                # 转换为RGB
                rgb_img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                
                # 检测人脸
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
                faces = face_cascade.detectMultiScale(gray, 1.3, 5)
                
                if len(faces) == 0:
                    return None
                
                # 处理检测到的人脸
                for (x, y, w, h) in faces:
                    # 提取人脸区域并调整大小
                    face_roi = rgb_img[y:y+h, x:x+w]
                    face_img = Image.fromarray(face_roi).resize((48, 48))
                    
                    # 预处理图像
                    img_np = np.array(face_img) / 255.0
                    img_input = img_np.transpose((2, 0, 1))
                    img_input = np.expand_dims(img_input, axis=0)
                    img_input = Tensor(img_input, dtype=ms.float32)
                    
                    # 进行预测
                    output = net(img_input)
                    probabilities = nn.Softmax()(output).asnumpy()[0]
                    
                    # 绘制人脸框和情绪标签
                    cv2.rectangle(img, (x, y), (x+w, y+h), (0, 255, 0), 2)
                    main_emotion = categories[np.argmax(probabilities)]
                    emotion_text = f"{main_emotion}: {probabilities[np.argmax(probabilities)]:.2f}"
                    cv2.putText(img, emotion_text, (x, y-10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
                    
                    # 保存结果
                    return {
                        "image": img,
                        "emotion": main_emotion,
                        "probabilities": probabilities
                    }
            except Exception as e:
                print(f"处理图片 {image_path} 时出错: {str(e)}")
                return None
        
        # 绑定选择事件
        def on_image_select(event):
            selection = image_listbox.curselection()
            if not selection:
                return
            
            index = selection[0]
            image_name = image_files[index]
            image_path = os.path.join(folder_path, image_name)
            
            # 如果结果已经存在，直接使用
            if image_name in results:
                result = results[image_name]
            else:
                # 处理图片
                result = process_image(image_path)
                results[image_name] = result
            
            if result:
                # 显示图像
                img = cv2.cvtColor(result["image"], cv2.COLOR_BGR2RGB)
                img = Image.fromarray(img)
                img.thumbnail((600, 400))  # 调整图像大小以适应窗口
                img = ImageTk.PhotoImage(img)
                image_label.config(image=img)
                image_label.image = img
                
                # 显示结果
                result_text.config(state=tk.NORMAL)
                result_text.delete(1.0, tk.END)
                result_text.insert(tk.END, f"文件名: {image_name}\n\n"
                                         f"主要情绪: {result['emotion']}\n\n"
                                         f"情绪概率分布:\n"
                                         f"生气: {result['probabilities'][0]:.2f}\n"
                                         f"开心: {result['probabilities'][1]:.2f}\n"
                                         f"悲伤: {result['probabilities'][2]:.2f}")
                result_text.config(state=tk.DISABLED)
            else:
                image_label.config(text="无法检测到人脸")
                result_text.config(state=tk.NORMAL)
                result_text.delete(1.0, tk.END)
                result_text.insert(tk.END, f"文件名: {image_name}\n\n"
                                         f"无法检测到人脸")
                result_text.config(state=tk.DISABLED)
        
        image_listbox.bind('<<ListboxSelect>>', on_image_select)
        
        # 自动选择第一个图像
        if image_files:
            image_listbox.selection_set(0)
            image_listbox.event_generate('<<ListboxSelect>>')

# 主函数
def main():
    # 确保中文显示正常
    plt.rcParams["font.family"] = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC"]
    
    # 创建主窗口
    root = tk.Tk()
    root.title("情绪识别系统")
    root.geometry("1024x768")
    
    # 初始化数据库连接
    db = DatabaseConnection()
    if not db.connect():
        messagebox.showerror("错误", "无法连接到数据库，请检查配置")
        return
    
    # 创建必要的表
    db.create_tables()
    
    # 初始化用户管理器
    user_manager = UserManager(db)
    
    # 加载模型
    global net
    net = CNN()
    
    # 尝试加载预训练模型
    try:
        param_dict = load_checkpoint("emotion_model.ckpt")
        load_param_into_net(net, param_dict)
        print("已加载预训练模型")
    except Exception as e:
        print(f"加载预训练模型失败: {e}")
        print("使用未训练的模型")
    
    # 创建 EmotionUI 实例
    emotion_ui = EmotionUI(root, user_manager)
    
    # 创建登录界面，将 emotion_ui 作为 main_app 传递
    LoginUI(root, user_manager, emotion_ui)
    
    # 运行主循环
    root.mainloop()

if __name__ == "__main__":
    main()